# OAuth2 Token Exchange Integration Testing Guide for AI Coding Assistants

## Overview

This guide provides AI coding assistants with implementation patterns for generating comprehensive integration tests for OAuth2 token exchange workflows with Keycloak. The focus is on practical implementation specifics rather than conceptual explanations.

## Prerequisites

- Keycloak 26.2+ with testcontainers
- keycloak-config-cli for realm initialization
- Understanding of OAuth2 flows, JWT tokens, and Keycloak client types

## 1. Testcontainers Setup

### Base Test Infrastructure

```typescript
import { GenericContainer, StartedTestContainer } from 'testcontainers';

class KeycloakTestContainer {
  private container: StartedTestContainer;
  
  async start(): Promise<void> {
    this.container = await new GenericContainer('quay.io/keycloak/keycloak:26.2.5')
      .withEnvironment({
        'KC_BOOTSTRAP_ADMIN_PASSWORD': 'admin',
        'KC_BOOTSTRAP_ADMIN_USERNAME': 'admin',
        'KC_HOSTNAME': 'http://localhost:8080',
        'APP_ENV': 'test'
      })
      .withExposedPorts(8080)
      .withCommand(['start-dev'])
      .start();
  }
  
  getKeycloakUrl(): string {
    return `http://localhost:${this.container.getMappedPort(8080)}`;
  }
}
```

### Realm Initialization with keycloak-config-cli

```typescript
async function initializeRealm(keycloakUrl: string, realmConfig: any): Promise<void> {
  const configContainer = await new GenericContainer('adorsys/keycloak-config-cli:latest-26.2.5')
    .withEnvironment({
      'KEYCLOAK_URL': keycloakUrl,
      'KEYCLOAK_USER': 'admin',
      'KEYCLOAK_PASSWORD': 'admin',
      'KEYCLOAK_AVAILABILITYCHECK_ENABLED': 'true',
      'IMPORT_PATH': '/config'
    })
    .withBindMounts([{
      source: path.resolve('./test-realm-config.json'),
      target: '/config/realm.json'
    }])
    .start();
    
  // Wait for import completion
  await configContainer.stop();
}
```

## 2. Client Registration Flow

### Dev Console Token Acquisition

```typescript
interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

// Direct Access Grant (Test Environment)
async function getDevConsoleToken(keycloakUrl: string): Promise<string> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(`${DEV_CONSOLE_CLIENT_ID}:${DEV_CONSOLE_CLIENT_SECRET}`)}`
    },
    body: new URLSearchParams({
      grant_type: 'password',
      username: '<EMAIL>',
      password: 'test-password'
    })
  });
  
  const tokenData: TokenResponse = await response.json();
  return tokenData.access_token;
}

// OAuth Flow (Non-Test Environment)
async function getDevConsoleTokenOAuth(keycloakUrl: string): Promise<string> {
  // Implementation depends on OAuth2 library
  // Standard Authorization Code flow with PKCE
  const authUrl = `${keycloakUrl}/realms/bodhi/protocol/openid-connect/auth`;
  const tokenUrl = `${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`;
  
  // Redirect to authUrl, handle callback, exchange code for token
  // Return access_token from token response
}
```

### App Client Registration

```typescript
interface AppClientRequest {
  name: string;
  description: string;
  redirect_uris: string[];
}

interface AppClientResponse {
  client_id: string;
  name: string;
  redirect_uris: string[];
}

async function registerAppClient(
  keycloakUrl: string, 
  devConsoleToken: string, 
  request: AppClientRequest
): Promise<AppClientResponse> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/apps`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${devConsoleToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(request)
  });
  
  if (!response.ok) {
    throw new Error(`App client registration failed: ${response.status}`);
  }
  
  return await response.json();
}
```

### Resource Server Registration

```typescript
interface ResourceClientRequest {
  name: string;
  description: string;
  redirect_uris: string[];
}

interface ResourceClientResponse {
  client_id: string;
  client_secret: string;
  name: string;
  scope: string; // scope_{client-id}
}

async function registerResourceClient(
  keycloakUrl: string,
  request: ResourceClientRequest
): Promise<ResourceClientResponse> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/resources`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(request)
  });
  
  if (!response.ok) {
    throw new Error(`Resource client registration failed: ${response.status}`);
  }
  
  return await response.json();
}
```

## 3. Permission Setup

### Resource Client Service Account Token

```typescript
async function getResourceServiceToken(
  keycloakUrl: string,
  clientId: string,
  clientSecret: string
): Promise<string> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(`${clientId}:${clientSecret}`)}`
    },
    body: new URLSearchParams({
      grant_type: 'client_credentials',
      scope: 'service_account'
    })
  });
  
  const tokenData: TokenResponse = await response.json();
  return tokenData.access_token;
}
```

### Request Access (Dynamic Audience Setup)

```typescript
interface RequestAccessRequest {
  app_client_id: string;
}

interface RequestAccessResponse {
  scope: string;
  status: string;
}

async function requestAudienceAccess(
  keycloakUrl: string,
  resourceServiceToken: string,
  appClientId: string
): Promise<RequestAccessResponse> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/resources/request-access`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resourceServiceToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      app_client_id: appClientId
    })
  });
  
  if (!response.ok) {
    throw new Error(`Request access failed: ${response.status}`);
  }
  
  return await response.json();
}
```

### User Permission Setup

```typescript
async function makeResourceAdmin(
  keycloakUrl: string,
  resourceServiceToken: string,
  userEmail: string
): Promise<void> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/resources/make-admin`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resourceServiceToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: userEmail
    })
  });
  
  if (!response.ok) {
    throw new Error(`Make admin failed: ${response.status}`);
  }
}
```

## 4. Token Exchange Workflow

### App User Token with Resource Scope

```typescript
async function getAppUserTokenWithResourceScope(
  keycloakUrl: string,
  appClientId: string,
  username: string,
  password: string,
  resourceScope: string
): Promise<string> {
  const scopes = [
    'openid',
    'email', 
    'profile',
    'roles',
    'scope_user_user',
    resourceScope
  ].join(' ');
  
  const response = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'password',
      client_id: appClientId,
      username: username,
      password: password,
      scope: scopes
    })
  });
  
  const tokenData: TokenResponse = await response.json();
  return tokenData.access_token;
}
```

### Token Exchange Implementation

```typescript
interface TokenExchangeResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

async function exchangeToken(
  keycloakUrl: string,
  resourceClientId: string,
  resourceClientSecret: string,
  subjectToken: string
): Promise<string> {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(`${resourceClientId}:${resourceClientSecret}`)}`
    },
    body: new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
      subject_token: subjectToken,
      subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      requested_token_type: 'urn:ietf:params:oauth:token-type:access_token',
      scope: 'openid email profile roles scope_user_user'
    })
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Token exchange failed: ${error.error} - ${error.error_description}`);
  }
  
  const tokenData: TokenExchangeResponse = await response.json();
  return tokenData.access_token;
}
```

## 5. Token Validation and Testing

### JWT Token Validation

```typescript
import jwt from 'jsonwebtoken';

interface TokenClaims {
  iss: string;
  aud: string | string[];
  sub: string;
  azp: string;
  email: string;
  scope: string;
  resource_access: Record<string, { roles: string[] }>;
}

function validateExchangedToken(
  token: string,
  expectedIssuer: string,
  expectedAudience: string,
  expectedClientId: string
): TokenClaims {
  const decoded = jwt.decode(token) as TokenClaims;
  
  // Validate issuer
  if (decoded.iss !== expectedIssuer) {
    throw new Error(`Invalid issuer. Expected: ${expectedIssuer}, Got: ${decoded.iss}`);
  }
  
  // Validate audience
  const audiences = Array.isArray(decoded.aud) ? decoded.aud : [decoded.aud];
  if (!audiences.includes(expectedAudience)) {
    throw new Error(`Invalid audience. Expected: ${expectedAudience}, Got: ${decoded.aud}`);
  }
  
  // Validate azp (authorized party)
  if (decoded.azp !== expectedClientId) {
    throw new Error(`Invalid azp. Expected: ${expectedClientId}, Got: ${decoded.azp}`);
  }
  
  // Validate required scopes
  const scopes = decoded.scope?.split(' ') || [];
  if (!scopes.includes('scope_user_user')) {
    throw new Error(`Missing required scope: scope_user_user. Available: ${decoded.scope}`);
  }
  
  return decoded;
}
```

## 6. Complete Integration Test Example

### Full Test Implementation

```typescript
describe('OAuth2 Token Exchange Integration', () => {
  let keycloak: KeycloakTestContainer;
  let keycloakUrl: string;

  beforeAll(async () => {
    keycloak = new KeycloakTestContainer();
    await keycloak.start();
    keycloakUrl = keycloak.getKeycloakUrl();

    // Initialize realm with test configuration
    await initializeRealm(keycloakUrl, testRealmConfig);
  });

  afterAll(async () => {
    await keycloak.stop();
  });

  test('complete token exchange workflow', async () => {
    // 1. Get dev console token
    const devConsoleToken = await getDevConsoleToken(keycloakUrl);

    // 2. Register app client
    const appClient = await registerAppClient(keycloakUrl, devConsoleToken, {
      name: 'Test App Client',
      description: 'Integration test app client',
      redirect_uris: ['http://localhost:3000/callback']
    });

    // 3. Register resource server
    const resourceClient = await registerResourceClient(keycloakUrl, {
      name: 'Test Resource Server',
      description: 'Integration test resource server',
      redirect_uris: ['http://localhost:8080/callback']
    });

    // 4. Get resource service token
    const resourceServiceToken = await getResourceServiceToken(
      keycloakUrl,
      resourceClient.client_id,
      resourceClient.client_secret
    );

    // 5. Setup user permissions
    await makeResourceAdmin(keycloakUrl, resourceServiceToken, '<EMAIL>');

    // 6. Request audience access
    const accessResponse = await requestAudienceAccess(
      keycloakUrl,
      resourceServiceToken,
      appClient.client_id
    );

    // 7. Get app user token with resource scope
    const appUserToken = await getAppUserTokenWithResourceScope(
      keycloakUrl,
      appClient.client_id,
      '<EMAIL>',
      'test-password',
      accessResponse.scope
    );

    // 8. Validate app token includes resource in audience
    const appTokenClaims = jwt.decode(appUserToken) as TokenClaims;
    const audiences = Array.isArray(appTokenClaims.aud) ? appTokenClaims.aud : [appTokenClaims.aud];
    expect(audiences).toContain(resourceClient.client_id);

    // 9. Perform token exchange
    const exchangedToken = await exchangeToken(
      keycloakUrl,
      resourceClient.client_id,
      resourceClient.client_secret,
      appUserToken
    );

    // 10. Validate exchanged token
    const expectedIssuer = `${keycloakUrl}/realms/bodhi`;
    const exchangedClaims = validateExchangedToken(
      exchangedToken,
      expectedIssuer,
      resourceClient.client_id,
      resourceClient.client_id
    );

    // 11. Verify token claims
    expect(exchangedClaims.email).toBe('<EMAIL>');
    expect(exchangedClaims.resource_access[resourceClient.client_id].roles).toContain('resource_admin');

    // 12. Test API call with exchanged token
    const apiResponse = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/userinfo`, {
      headers: {
        'Authorization': `Bearer ${exchangedToken}`
      }
    });

    expect(apiResponse.ok).toBe(true);
  });
});
```

## 7. Error Handling and Common Pitfalls

### Token Exchange Errors

```typescript
interface TokenExchangeError {
  error: string;
  error_description: string;
}

async function handleTokenExchangeErrors(response: Response): Promise<never> {
  const error: TokenExchangeError = await response.json();

  switch (error.error) {
    case 'invalid_client':
      throw new Error('Resource client authentication failed. Check client_id and client_secret.');

    case 'invalid_grant':
      if (error.error_description?.includes('audience')) {
        throw new Error('Subject token audience does not include resource client. Call request-access first.');
      }
      throw new Error(`Invalid grant: ${error.error_description}`);

    case 'invalid_scope':
      throw new Error('Requested scope not available. Verify scope configuration.');

    default:
      throw new Error(`Token exchange failed: ${error.error} - ${error.error_description}`);
  }
}
```

### Audience Validation Issues

```typescript
function validateTokenAudience(token: string, expectedAudience: string): void {
  const claims = jwt.decode(token) as TokenClaims;
  const audiences = Array.isArray(claims.aud) ? claims.aud : [claims.aud];

  if (!audiences.includes(expectedAudience)) {
    throw new Error(
      `Token audience validation failed. ` +
      `Expected: ${expectedAudience}, ` +
      `Got: ${audiences.join(', ')}. ` +
      `Ensure request-access was called before token acquisition.`
    );
  }
}
```

### Scope Configuration Issues

```typescript
function validateRequiredScopes(token: string, requiredScopes: string[]): void {
  const claims = jwt.decode(token) as TokenClaims;
  const tokenScopes = claims.scope?.split(' ') || [];

  const missingScopes = requiredScopes.filter(scope => !tokenScopes.includes(scope));

  if (missingScopes.length > 0) {
    throw new Error(
      `Missing required scopes: ${missingScopes.join(', ')}. ` +
      `Available scopes: ${tokenScopes.join(', ')}. ` +
      `Verify client scope configuration and request-access call.`
    );
  }
}
```

## 8. Test Configuration Templates

### Realm Configuration for Tests

```typescript
const testRealmConfig = {
  realm: 'bodhi',
  enabled: true,
  clients: [
    {
      clientId: 'client-bodhi-dev-console',
      enabled: true,
      publicClient: false,
      directAccessGrantsEnabled: true, // Enable for test environment
      serviceAccountsEnabled: true,
      clientAuthenticatorType: 'client-secret',
      secret: 'dev-console-secret'
    }
  ],
  users: [
    {
      username: '<EMAIL>',
      email: '<EMAIL>',
      enabled: true,
      credentials: [{
        type: 'password',
        value: 'test-password',
        temporary: false
      }]
    }
  ],
  clientScopes: [
    {
      name: 'scope_user_user',
      protocol: 'openid-connect',
      attributes: {
        'include.in.token.scope': 'true'
      }
    }
  ]
};
```

### Environment-Specific Configuration

```typescript
interface TestEnvironment {
  keycloakUrl: string;
  realm: string;
  devConsoleClientId: string;
  devConsoleClientSecret: string;
  testUser: {
    email: string;
    password: string;
  };
}

const testEnv: TestEnvironment = {
  keycloakUrl: process.env.KEYCLOAK_URL || 'http://localhost:8080',
  realm: 'bodhi',
  devConsoleClientId: 'client-bodhi-dev-console',
  devConsoleClientSecret: process.env.DEV_CONSOLE_SECRET || 'dev-console-secret',
  testUser: {
    email: '<EMAIL>',
    password: 'test-password'
  }
};
```

## 9. Best Practices for AI Implementation

### Test Structure Guidelines

1. **Isolation**: Each test should create its own clients and users
2. **Cleanup**: Use testcontainers for automatic cleanup
3. **Validation**: Validate both HTTP responses and JWT token content
4. **Error Testing**: Include negative test cases for common failure scenarios
5. **Scope Testing**: Verify scope-based access control works correctly

### Performance Considerations

1. **Container Reuse**: Reuse Keycloak container across test suite when possible
2. **Parallel Execution**: Design tests for parallel execution with unique client IDs
3. **Token Caching**: Cache service account tokens within test scope
4. **Minimal Setup**: Only create necessary clients and users per test

### Security Testing

1. **Token Validation**: Always validate JWT signatures and claims
2. **Audience Verification**: Ensure tokens have correct audience claims
3. **Scope Enforcement**: Test that scope restrictions are properly enforced
4. **Cross-Client Access**: Verify that clients cannot access unauthorized resources

This guide provides the foundation for implementing comprehensive OAuth2 token exchange integration tests. AI assistants should adapt these patterns to specific application requirements while maintaining the core security and validation principles.
```
